import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl;

  // Only handle www.updraft.fund root path requests that weren't redirected
  // If we reach here, it means:
  // 1. hostname === 'www.updraft.fund'
  // 2. pathname === '/'
  // 3. No hasUsedApp cookie (otherwise would have been redirected)
  // So we should serve the landing page

  if (hostname === 'www.updraft.fund' && pathname === '/') {
    // Serve the landing page (public/landing/index.html is served at /landing/index.html)
    return NextResponse.rewrite(new URL('/landing/index.html', request.url));
  }

  // For all other cases, continue normal processing
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Only match the root path for www.updraft.fund
     * All other paths are handled by redirects in vercel.json
     */
    '/',
  ],
};
