{"rewrites": [{"source": "/idea/(.*)", "destination": "/api/social-meta?type=idea&id=$1", "has": [{"type": "header", "key": "user-agent", "value": ".*(facebookexternalhit|twitterbot|linkedinbot|slackbot|discordbot|telegrambot|whatsapp|skypeuripreview|applebot|googlebot|bingbot|yandexbot|pinterest|redditbot|mastodon|misskey|pleroma).*"}]}, {"source": "/solution/(.*)", "destination": "/api/social-meta?type=solution&id=$1", "has": [{"type": "header", "key": "user-agent", "value": ".*(facebookexternalhit|twitterbot|linkedinbot|slackbot|discordbot|telegrambot|whatsapp|skypeuripreview|applebot|googlebot|bingbot|yandexbot|pinterest|redditbot|mastodon|misskey|pleroma).*"}]}, {"source": "/(.*)", "destination": "/api/smart-routing", "has": [{"type": "host", "value": "www.updraft.fund"}]}, {"source": "/(.*)", "destination": "/index.html", "has": [{"type": "host", "value": "(?!www\\.updraft\\.fund).*"}]}]}