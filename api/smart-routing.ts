// Vercel Edge Function for smart routing between landing page and app
import type { VercelRequest, VercelResponse } from '@vercel/node';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Checks if the user has the 'hasUsedApp' cookie indicating they've used the app before
 */
function hasUsedAppCookie(req: VercelRequest): boolean {
  const cookies = req.headers.cookie || '';
  // More robust cookie parsing to avoid false positives
  const cookieArray = cookies.split(';').map(c => c.trim());
  return cookieArray.some(cookie => cookie === 'hasUsedApp=true');
}

/**
 * Serves the landing page HTML file
 */
function serveLandingPage(res: VercelResponse): void {
  try {
    // Read the landing page file from the public directory
    const landingPagePath = join(
      process.cwd(),
      'public',
      'landing',
      'index.html'
    );
    console.log('Attempting to read landing page from:', landingPagePath);
    const landingPageContent = readFileSync(landingPagePath, 'utf8');
    console.log('Successfully read landing page, length:', landingPageContent.length);

    res.setHeader('Content-Type', 'text/html');
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour - landing page is static
    res.send(landingPageContent);
  } catch (error) {
    console.error('Error serving landing page:', error);
    console.error('Current working directory:', process.cwd());
    // Fallback: redirect to app
    res.redirect(302, 'https://app.updraft.fund/');
  }
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Parse the URL properly - req.url is just the path + query, not full URL
  const pathname = req.url?.split('?')[0] || '/';
  const search = req.url?.includes('?') ? req.url.substring(req.url.indexOf('?')) : '';
  const hostname = req.headers.host || '';

  console.log('Smart routing:', { hostname, pathname, search, cookies: req.headers.cookie });

  // Only handle www.updraft.fund requests
  if (hostname !== 'www.updraft.fund') {
    return res.redirect(302, `https://app.updraft.fund${pathname}${search}`);
  }

  // For www.updraft.fund root path
  if (pathname === '/') {
    // If user hasn't used the app before, show landing page
    if (!hasUsedAppCookie(req)) {
      console.log('Serving landing page - no cookie found');
      return serveLandingPage(res);
    }
    // Otherwise redirect to app
    console.log('Redirecting to app - cookie found');
    return res.redirect(302, 'https://app.updraft.fund/');
  }

  // For all other www.updraft.fund paths, redirect to app (preserves social links)
  return res.redirect(302, `https://app.updraft.fund${pathname}${search}`);
}
