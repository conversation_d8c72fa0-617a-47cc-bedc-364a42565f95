import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { hostname } = request.nextUrl;

  // If we reach here, it means:
  // 1. pathname === '/' (guaranteed by matcher)
  // 2. No hasUsedApp cookie (otherwise would have been redirected)
  // 3. Only serve landing page for www.updraft.fund

  if (hostname === 'www.updraft.fund') {
    // Serve the landing page (public/landing/index.html is served at /landing/index.html)
    return NextResponse.rewrite(new URL('/landing/index.html', request.url));
  }

  // For other hostnames (app.updraft.fund, preview deployments), continue normal processing
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Only match the root path for www.updraft.fund
     * All other paths are handled by redirects in vercel.json
     */
    '/',
  ],
};
