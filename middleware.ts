import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl;

  // Only handle www.updraft.fund requests
  if (hostname !== 'www.updraft.fund') {
    return NextResponse.next();
  }

  // Only handle root path requests that haven't been redirected
  if (pathname !== '/') {
    return NextResponse.next();
  }

  // Check if user has the hasUsedApp cookie
  const hasUsedAppCookie = request.cookies.get('hasUsedApp');
  const hasUsedApp = hasUsedAppCookie?.value === 'true';

  // If user has used the app, they should have been redirected by vercel.json
  // If we reach here, they don't have the cookie, so serve landing page
  if (!hasUsedApp) {
    // Serve the landing page
    return NextResponse.rewrite(new URL('/landing/index.html', request.url));
  }

  // Fallback: continue to normal processing (shouldn't happen due to redirects)
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - landing (landing page assets)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|landing).*)',
  ],
};
